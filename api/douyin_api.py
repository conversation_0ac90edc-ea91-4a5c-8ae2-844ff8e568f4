# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：  
# 1. 不得用于任何商业用途。  
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。  
# 3. 不得进行大规模爬取或对平台造成运营干扰。  
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。   
# 5. 不得用于任何非法或不当的用途。
#   
# 详细许可条款请参阅项目根目录下的LICENSE文件。  
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。  

import asyncio
import json
import base64
import time
from typing import Dict, List, Optional, Union

from fastapi import FastAPI, HTTPException, status, BackgroundTasks, APIRouter
from pydantic import BaseModel
import uvicorn

import config
from media_platform.douyin import DouYinCrawler
from tools import utils

# 创建主应用
app = FastAPI(
    title="抖音创作者平台API",
    description="抖音创作者平台相关的HTTP API服务，包括二维码登录、数据获取等功能",
    version="1.0.0"
)

# 创建抖音API路由器
douyin_router = APIRouter(prefix="/api/douyin", tags=["抖音创作者平台"])
# 创建一个全局的爬虫实例字典，用于存储不同会话的爬虫实例
crawler_instances = {}
# 默认的爬虫实例，用于兼容旧代码
douyin_crawler = DouYinCrawler()

class CreatorLoginRequest(BaseModel):
    login_type: str  # "qrcode" 或 "cookies"
    cookies: Optional[str] = None
    use_cdp: bool = False
    proxy: Optional[str] = None
    session_id: Optional[str] = None  # 可选的会话ID，用于标识不同的客户端


class CreatorDataRequest(BaseModel):
    cookies: str
    use_cdp: bool = False
    proxy: Optional[str] = None
    session_id: Optional[str] = None  # 可选的会话ID，用于标识不同的客户端


class QRCodeLoginRequest(BaseModel):
    """获取二维码登录信息的请求模型"""
    use_cdp: bool = False
    proxy: Optional[str] = None
    session_id: Optional[str] = None


class QRCodeStatusRequest(BaseModel):
    """检查二维码登录状态的请求模型"""
    session_id: str


@douyin_router.post("/creator/qrcode/get")
async def get_qrcode_info(request: QRCodeLoginRequest):
    """
    获取抖音创作者平台登录二维码
    
    适合外部API调用的二维码登录流程：
    1. 客户端调用此接口获取二维码图片
    2. 客户端在自己的界面显示二维码供用户扫码
    3. 客户端轮询检查登录状态
    4. 登录成功后获取cookies
    
    注意：每次调用此接口都会创建全新的浏览器实例，确保并发安全
    """
    playwright_instance = None
    browser_context = None
    try:
        # 生成会话ID，如果没有提供
        session_id = request.session_id or utils.generate_random_str(16)
        
        # 如果会话已存在，先清理旧的资源
        if session_id in crawler_instances:
            utils.logger.info(f"[DouYinAPI.get_qrcode_info] 清理已存在的会话: {session_id}")
            await _cleanup_session_resources(session_id)
        
        # 设置全局配置
        if request.use_cdp:
            config.ENABLE_CDP_MODE = True
        else:
            config.ENABLE_CDP_MODE = False
            
        if request.proxy:
            config.ENABLE_IP_PROXY = True
            
        # 创建新的爬虫实例
        crawler = DouYinCrawler()
        
        # 初始化会话信息
        crawler_instances[session_id] = {
            'crawler': crawler,
            'status': 'qrcode_generating',
            'cookies': None,
            'browser_context': None,
            'context_page': None,
            'playwright_instance': None,
            'qrcode_timestamp': int(time.time())  # 记录二维码生成时间
        }
        
        # 启动全新的浏览器实例 - 手动管理playwright生命周期
        from playwright.async_api import async_playwright
        
        playwright_proxy_format, httpx_proxy_format = None, None
        if config.ENABLE_IP_PROXY:
            from proxy.proxy_ip_pool import create_ip_pool, IpInfoModel
            ip_proxy_pool = await create_ip_pool(
                config.IP_PROXY_POOL_COUNT, enable_validate_ip=True
            )
            ip_proxy_info: IpInfoModel = await ip_proxy_pool.get_proxy()
            playwright_proxy_format, httpx_proxy_format = crawler.format_proxy_info(
                ip_proxy_info
            )

        # 手动启动playwright实例，不使用上下文管理器
        utils.logger.info(f"[DouYinAPI.get_qrcode_info] 为会话 {session_id} 创建全新浏览器实例")
        
        playwright_instance = await async_playwright().start()
        
        # 根据配置选择启动模式
        if config.ENABLE_CDP_MODE:
            browser_context = await crawler.launch_browser_with_cdp(
                playwright_instance,
                playwright_proxy_format,
                None,
                headless=config.CDP_HEADLESS,
            )
        else:
            # Launch a browser context.
            chromium = playwright_instance.chromium
            browser_context = await crawler.launch_browser(
                chromium,
                playwright_proxy_format,
                user_agent=None,
                headless=True,  # API模式强制使用headless
            )
        
        # stealth.min.js is a js script to prevent the website from detecting the crawler.
        await browser_context.add_init_script(path="libs/stealth.min.js")
        context_page = await browser_context.new_page()
        
        # 1. 清理所有cookie
        await browser_context.clear_cookies()

        # 2. 访问二维码页面
        utils.logger.info(f"[DouYinAPI.get_qrcode_info] 访问创作者登录页面（全新环境）")
        await context_page.goto(crawler.creator_login_url)

        # 3. 等待页面基本加载
        await context_page.wait_for_load_state('domcontentloaded', timeout=10000)

        # 4. 清理localStorage和sessionStorage（此时页面已允许访问localStorage）
        try:
            await context_page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
            # 刷新页面，确保二维码处于未登录态
            await context_page.reload()
            await context_page.wait_for_load_state('domcontentloaded', timeout=10000)
        except Exception as e:
            utils.logger.warning(f"[DouYinAPI.get_qrcode_info] 清理localStorage/sessionStorage失败: {e}")

        # 5. 等待网络请求基本完成
        try:
            await context_page.wait_for_load_state('networkidle', timeout=8000)
        except:
            utils.logger.info("[DouYinAPI.get_qrcode_info] 网络状态等待超时，继续处理")
            pass
        
        # 使用与 DouYinLogin.login_by_qrcode 完全相同的方式获取二维码
        qrcode_img_selector = "xpath=//div[@id='animate_qrcode_container']//img"
        utils.logger.info(f"[DouYinAPI.get_qrcode_info] 使用与 DouYinLogin.login_by_qrcode 相同的方式获取二维码: {qrcode_img_selector}")
        
        qrcode_src = await utils.find_login_qrcode(
            context_page,
            selector=qrcode_img_selector
        )
        
        if not qrcode_src:
            utils.logger.error("[DouYinAPI.get_qrcode_info] 未找到登录二维码，请确认...")
            # 生成调试信息
            page_title = await context_page.title()
            page_url = context_page.url
            screenshot_path = f"debug_qrcode_{utils.generate_random_str(8)}.png"
            await context_page.screenshot(path=screenshot_path)
            
            # 获取页面HTML用于调试
            page_content = await context_page.content()
            
            # 尝试查找页面中所有的img元素，帮助调试
            all_images = await context_page.query_selector_all("img")
            image_info = []
            for i, img in enumerate(all_images[:10]):  # 只取前10个图片
                try:
                    src = await img.get_attribute('src') or ''
                    alt = await img.get_attribute('alt') or ''
                    class_name = await img.get_attribute('class') or ''
                    image_info.append(f"图片{i+1}: src='{src[:50]}...', alt='{alt}', class='{class_name}'")
                except:
                    image_info.append(f"图片{i+1}: 获取属性失败")
            
            utils.logger.error(f"[DouYinAPI.get_qrcode_info] 调试信息:")
            utils.logger.error(f"  页面标题: {page_title}")
            utils.logger.error(f"  页面URL: {page_url}")
            utils.logger.error(f"  页面截图: {screenshot_path}")
            utils.logger.error(f"  页面内容长度: {len(page_content)}")
            utils.logger.error(f"  页面中的图片元素:")
            for img_info in image_info:
                utils.logger.error(f"    {img_info}")
            
            # 清理资源
            await browser_context.close()
            await playwright_instance.stop()
            if session_id in crawler_instances:
                del crawler_instances[session_id]
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"无法找到登录二维码。页面标题: {page_title}，URL: {page_url}。已生成调试截图: {screenshot_path}"
            )
        
        utils.logger.info(f"[DouYinAPI.get_qrcode_info] ✅ 成功获取二维码！使用选择器: {qrcode_img_selector}")
        used_selector = qrcode_img_selector
        
        # 保存浏览器上下文到会话，用于后续状态检查
        crawler_instances[session_id]['browser_context'] = browser_context
        crawler_instances[session_id]['context_page'] = context_page
        crawler_instances[session_id]['playwright_instance'] = playwright_instance
        crawler_instances[session_id]['used_selector'] = used_selector
        crawler_instances[session_id]['status'] = 'qrcode_generated'
        
        utils.logger.info(f"[DouYinAPI.get_qrcode_info] 会话 {session_id} 二维码生成成功，浏览器实例已保存")
        
        # 确保返回的是完整的base64数据URL格式
        if qrcode_src and not qrcode_src.startswith('data:image/'):
            qrcode_src = f"data:image/png;base64,{qrcode_src}"
        
        # 注意：这里不关闭浏览器和playwright，保留给状态检查使用
        return {
            "status": "success",
            "message": "二维码获取成功",
            "session_id": session_id,
            "qrcode_image": qrcode_src,
            "expires_in": 300,
            "debug_info": {
                "used_selector": used_selector,
                "page_title": await context_page.title(),
                "page_url": context_page.url,
                "browser_instance": "new_instance_created",
                "implementation": "using_project_standard_find_login_qrcode_function"
            }
        }
            
    except Exception as e:
        utils.logger.error(f"[DouYinAPI.get_qrcode_info] 获取二维码失败: {str(e)}")
        # 清理资源
        if browser_context:
            try:
                await browser_context.close()
            except:
                pass
        if playwright_instance:
            try:
                await playwright_instance.stop()
            except:
                pass
        if 'session_id' in locals() and session_id in crawler_instances:
            del crawler_instances[session_id]
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取二维码失败: {str(e)}"
        )


async def _cleanup_session_resources(session_id: str):
    """清理指定会话的所有资源"""
    try:
        if session_id not in crawler_instances:
            return
            
        session_info = crawler_instances[session_id]
        
        # 清理浏览器上下文
        browser_context = session_info.get('browser_context')
        if browser_context:
            try:
                await browser_context.close()
                utils.logger.info(f"[_cleanup_session_resources] 已清理会话 {session_id} 的浏览器上下文")
            except Exception as e:
                utils.logger.warning(f"[_cleanup_session_resources] 清理浏览器上下文失败: {e}")
        
        # 清理playwright实例（必须在浏览器上下文之后清理）
        playwright_instance = session_info.get('playwright_instance')
        if playwright_instance:
            try:
                await playwright_instance.stop()
                utils.logger.info(f"[_cleanup_session_resources] 已清理会话 {session_id} 的playwright实例")
            except Exception as e:
                utils.logger.warning(f"[_cleanup_session_resources] 清理playwright实例失败: {e}")
        
        # 清理爬虫实例
        crawler = session_info.get('crawler')
        if crawler:
            try:
                await crawler.close()
            except Exception as e:
                utils.logger.warning(f"[_cleanup_session_resources] 清理爬虫实例失败: {e}")
        
        # 删除会话
        del crawler_instances[session_id]
        utils.logger.info(f"[_cleanup_session_resources] 会话 {session_id} 资源清理完成")
        
    except Exception as e:
        utils.logger.error(f"[_cleanup_session_resources] 清理会话资源失败: {e}")


@douyin_router.post("/creator/qrcode/status")
async def check_qrcode_status(request: QRCodeStatusRequest):
    """
    检查二维码登录状态
    
    客户端应该轮询此接口检查登录状态，直到登录成功或超时
    """
    try:
        if request.session_id not in crawler_instances:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在或已过期"
            )
            
        session_info = crawler_instances[request.session_id]
        browser_context = session_info.get('browser_context')
        
        if not browser_context:
            # 如果浏览器上下文不存在，但会话状态是已登录，说明已经完成登录
            if session_info.get('status') == 'logged_in' and session_info.get('cookies'):
                return {
                    "status": "success",
                    "login_status": "logged_in", 
                    "message": "登录成功（来自缓存）",
                    "cookies": session_info.get('cookies')
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="会话状态异常，请重新获取二维码"
                )
        
        # 检查浏览器上下文是否仍然有效
        try:
            # 测试浏览器上下文是否仍然可用
            pages = browser_context.pages
            if not pages:
                raise Exception("浏览器页面已关闭")
                
            # 检查登录状态
            current_cookies = await browser_context.cookies()
            _, cookie_dict = utils.convert_cookies(current_cookies)
            
            # 检查是否已登录 - 使用更宽松的判断条件
            login_indicators = [
                cookie_dict.get("sessionid") is not None,
                any("login" in key.lower() and val for key, val in cookie_dict.items()),
            ]
            
            if any(login_indicators):
                # 登录成功
                cookie_str, _ = utils.convert_cookies(current_cookies)
                
                # 更新会话状态
                session_info['status'] = 'logged_in'
                session_info['cookies'] = cookie_str
                
                # 安全地关闭浏览器和playwright实例
                try:
                    await browser_context.close()
                    utils.logger.info("[DouYinAPI.check_qrcode_status] 登录成功，已关闭浏览器上下文")
                except Exception as e:
                    utils.logger.warning(f"[DouYinAPI.check_qrcode_status] 关闭浏览器上下文失败: {e}")
                
                # 关闭playwright实例
                playwright_instance = session_info.get('playwright_instance')
                if playwright_instance:
                    try:
                        await playwright_instance.stop()
                        utils.logger.info("[DouYinAPI.check_qrcode_status] 登录成功，已关闭playwright实例")
                    except Exception as e:
                        utils.logger.warning(f"[DouYinAPI.check_qrcode_status] 关闭playwright实例失败: {e}")
                
                session_info['browser_context'] = None
                session_info['context_page'] = None
                session_info['playwright_instance'] = None
                
                return {
                    "status": "success",
                    "login_status": "logged_in",
                    "message": "登录成功",
                    "cookies": cookie_str
                }
            else:
                # 还未登录
                return {
                    "status": "success", 
                    "login_status": "waiting",
                    "message": "等待用户扫码登录"
                }
                
        except Exception as browser_error:
            # 浏览器上下文已关闭或出现其他错误
            utils.logger.warning(f"[DouYinAPI.check_qrcode_status] 浏览器上下文错误: {browser_error}")
            
            # 检查是否是已知的浏览器关闭错误
            if "Target page, context or browser has been closed" in str(browser_error) or \
               "TargetClosedError" in str(browser_error):
                
                # 清理无效的浏览器引用
                session_info['browser_context'] = None
                session_info['context_page'] = None
                
                # 清理playwright实例
                playwright_instance = session_info.get('playwright_instance')
                if playwright_instance:
                    try:
                        await playwright_instance.stop()
                        utils.logger.info("[DouYinAPI.check_qrcode_status] 浏览器关闭错误，已清理playwright实例")
                    except Exception as e:
                        utils.logger.warning(f"[DouYinAPI.check_qrcode_status] 清理playwright实例失败: {e}")
                session_info['playwright_instance'] = None
                
                # 如果已经有cookies，说明之前登录成功了
                if session_info.get('cookies'):
                    session_info['status'] = 'logged_in'
                    return {
                        "status": "success",
                        "login_status": "logged_in",
                        "message": "登录成功（浏览器已关闭）",
                        "cookies": session_info.get('cookies')
                    }
                else:
                    # 浏览器关闭但没有cookies，需要重新获取二维码
                    return {
                        "status": "error",
                        "login_status": "expired", 
                        "message": "二维码已过期，请重新获取"
                    }
            else:
                # 其他错误，重新抛出
                raise browser_error
            
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        utils.logger.error(f"[DouYinAPI.check_qrcode_status] 检查登录状态失败: {str(e)}")
        
        # 清理会话
        if request.session_id in crawler_instances:
            await _cleanup_session_resources(request.session_id)
            
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查登录状态失败: {str(e)}"
        )


@douyin_router.post("/creator/login")
async def creator_login(request: CreatorLoginRequest):
    """
    抖音创作者平台登录接口
    
    注意：此接口已不推荐使用二维码登录，建议使用 /creator/qrcode/get 和 /creator/qrcode/status 接口
    """
    try:
        # 验证登录类型
        if request.login_type not in ["qrcode", "cookies"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="登录类型必须是 'qrcode' 或 'cookies'"
            )
            
        # 对于API服务，不建议使用二维码登录
        if request.login_type == "qrcode":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="API服务建议使用 /creator/qrcode/get 和 /creator/qrcode/status 接口进行二维码登录"
            )
            
        if request.login_type == "cookies" and not request.cookies:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="使用cookies登录时必须提供cookies参数"
            )
            
        # 生成会话ID，如果没有提供
        session_id = request.session_id or utils.generate_random_str(16)
        
        # 为每个会话创建一个新的爬虫实例
        crawler = DouYinCrawler()
        crawler_instances[session_id] = {
            'crawler': crawler,
            'status': 'logging_in',
            'cookies': None
        }
        
        # 设置全局配置
        if request.use_cdp:
            config.ENABLE_CDP_MODE = True
        else:
            config.ENABLE_CDP_MODE = False
            
        if request.proxy:
            config.ENABLE_IP_PROXY = True
        
        result = await crawler.creator_login(
            login_type=request.login_type,
            login_phone="",  # 创作者平台不支持手机号登录
            cookie_str=request.cookies
        )
        
        # 更新会话状态
        crawler_instances[session_id]['status'] = 'logged_in'
        crawler_instances[session_id]['cookies'] = result.get('cookies', '')
        
        return {
            "status": "success", 
            "cookies": result.get('cookies', ''),
            "session_id": session_id,
            "message": result.get('message', '登录成功')
        }
    except Exception as e:
        # 清理失败的会话
        if 'session_id' in locals() and session_id in crawler_instances:
            del crawler_instances[session_id]
            
        utils.logger.error(f"[DouYinAPI.creator_login] 登录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )


@douyin_router.post("/creator/data")
async def get_creator_data(request: CreatorDataRequest):
    """
    获取抖音创作者平台数据
    
    Args:
        request: 请求参数
            cookies: 登录成功后的cookies
            use_cdp: 是否使用CDP模式启动浏览器
            proxy: 代理地址，格式为 "http://ip:port" 或 "socks5://ip:port"
            session_id: 可选的会话ID，用于标识不同的客户端
    
    Returns:
        创作者平台数据，包括数据概览、内容列表和粉丝数据
    """
    try:
        if not request.cookies:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须提供cookies参数"
            )
            
        # 设置全局配置
        if request.use_cdp:
            config.ENABLE_CDP_MODE = True
        else:
            config.ENABLE_CDP_MODE = False
            
        if request.proxy:
            config.ENABLE_IP_PROXY = True
        
        # 获取对应会话的爬虫实例，如果没有则创建新实例
        crawler = None
        if request.session_id and request.session_id in crawler_instances:
            session_info = crawler_instances[request.session_id]
            if session_info['status'] == 'logged_in':
                # 使用已登录的会话中的cookies
                cookies_to_use = session_info.get('cookies') or request.cookies
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="会话未登录，请先调用登录接口"
                )
            crawler = session_info['crawler']
        else:
            # 创建新的爬虫实例
            crawler = DouYinCrawler()
            cookies_to_use = request.cookies
        
        result = await crawler.get_creator_data(
            cookie_str=cookies_to_use
        )
        
        return {"status": "success", "data": result}
    except Exception as e:
        utils.logger.error(f"[DouYinAPI.get_creator_data] 获取数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据失败: {str(e)}"
        )


@douyin_router.delete("/creator/session/{session_id}")
async def delete_session(session_id: str):
    """
    删除指定的会话，释放资源
    
    Args:
        session_id: 会话ID
    
    Returns:
        删除结果
    """
    try:
        if session_id in crawler_instances:
            await _cleanup_session_resources(session_id)
            return {"status": "success", "message": f"会话 {session_id} 已删除"}
        else:
            return {"status": "error", "message": f"会话 {session_id} 不存在"}
    except Exception as e:
        utils.logger.error(f"[DouYinAPI.delete_session] 删除会话失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除会话失败: {str(e)}"
        )


@douyin_router.get("/creator/sessions")
async def list_sessions():
    """
    列出所有活跃的会话
    
    Returns:
        会话列表，包含会话状态信息
    """
    try:
        sessions = []
        for session_id, session_info in crawler_instances.items():
            sessions.append({
                "session_id": session_id,
                "status": session_info.get('status', 'unknown'),
                "has_cookies": bool(session_info.get('cookies')),
                "has_browser_context": bool(session_info.get('browser_context'))
            })
        return {"status": "success", "sessions": sessions, "count": len(sessions)}
    except Exception as e:
        utils.logger.error(f"[DouYinAPI.list_sessions] 获取会话列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话列表失败: {str(e)}"
        )


@douyin_router.post("/creator/sessions/cleanup")
async def cleanup_sessions(expired_only: bool = False):
    """
    清理会话，释放资源
    
    Args:
        expired_only: 是否只清理过期会话（超过10分钟的会话）
    
    Returns:
        清理结果
    """
    try:
        cleaned_count = 0
        current_time = int(time.time())
        sessions_to_delete = list(crawler_instances.keys())
        
        for session_id in sessions_to_delete:
            try:
                session_info = crawler_instances[session_id]
                
                # 如果只清理过期会话，检查时间戳
                if expired_only:
                    session_time = session_info.get('qrcode_timestamp', 0)
                    if current_time - session_time < 600:  # 10分钟 = 600秒
                        continue
                
                await _cleanup_session_resources(session_id)
                cleaned_count += 1
                
            except Exception as e:
                utils.logger.warning(f"[DouYinAPI.cleanup_sessions] 清理会话 {session_id} 失败: {str(e)}")
        
        cleanup_type = "过期会话" if expired_only else "所有会话"
        return {
            "status": "success", 
            "message": f"清理了 {cleaned_count} 个{cleanup_type}",
            "cleaned_count": cleaned_count,
            "cleanup_type": cleanup_type
        }
    except Exception as e:
        utils.logger.error(f"[DouYinAPI.cleanup_sessions] 清理会话失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理会话失败: {str(e)}"
        )


@douyin_router.get("/")
async def root():
    return {"message": "抖音创作者平台API服务"}


# 将抖音路由器挂载到主应用
app.include_router(douyin_router)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)