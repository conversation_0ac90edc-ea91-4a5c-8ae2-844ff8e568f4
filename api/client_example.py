#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抖音创作者平台API客户端示例

此示例展示如何通过HTTP API调用抖音创作者平台功能，
适合外部系统集成和跨服务器场景使用。

功能：
1. 二维码登录流程
2. 获取创作者数据
3. 会话管理
"""

import requests
import time
import base64
import json
from typing import Optional
from pathlib import Path


class DouyinCreatorAPIClient:
    """抖音创作者平台API客户端"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        """
        初始化API客户端
        
        Args:
            api_base_url: API服务的基础URL
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.session_id = None
        self.cookies = None
        
    def _make_request(self, method: str, endpoint: str, data: dict = None) -> dict:
        """发起HTTP请求的通用方法"""
        url = f"{self.api_base_url}/api/douyin{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, timeout=30)
            elif method.upper() == "POST":
                response = requests.post(url, json=data, timeout=30)
            elif method.upper() == "DELETE":
                response = requests.delete(url, timeout=30)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"API请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    print(f"错误详情: {error_detail}")
                except:
                    print(f"响应内容: {e.response.text}")
            raise
    
    def login_with_qrcode(self, session_id: Optional[str] = None, 
                         use_cdp: bool = False, proxy: Optional[str] = None) -> bool:
        """
        使用二维码登录
        
        Args:
            session_id: 可选的会话ID，如果不提供会自动生成
            use_cdp: 是否使用CDP模式
            proxy: 代理地址
            
        Returns:
            bool: 登录是否成功
        """
        print("=== 开始二维码登录流程 ===")
        
        # 1. 获取二维码
        print("1. 获取登录二维码...")
        qrcode_data = {
            "use_cdp": use_cdp,
            "proxy": proxy
        }
        if session_id:
            qrcode_data["session_id"] = session_id
            
        result = self._make_request("POST", "/creator/qrcode/get", qrcode_data)
        
        if result["status"] != "success":
            print(f"获取二维码失败: {result}")
            return False
            
        self.session_id = result["session_id"]
        qrcode_image = result["qrcode_image"]
        
        print(f"二维码获取成功，会话ID: {self.session_id}")
        print(f"调试信息: {result.get('debug_info', {})}")
        
        # 2. 保存二维码图片
        self._save_qrcode_image(qrcode_image)
        print("二维码已保存为 qrcode.png，请使用抖音APP扫码登录")
        
        # 3. 轮询检查登录状态
        print("3. 等待用户扫码登录...")
        max_wait_time = 300  # 最多等待5分钟
        check_interval = 5   # 每5秒检查一次
        
        for i in range(0, max_wait_time, check_interval):
            print(f"检查登录状态... ({i//60}分{i%60}秒)")
            
            status_result = self._make_request("POST", "/creator/qrcode/status", {
                "session_id": self.session_id
            })
            
            if status_result["status"] != "success":
                print(f"检查登录状态失败: {status_result}")
                return False
                
            login_status = status_result.get("login_status")
            
            if login_status == "logged_in":
                self.cookies = status_result["cookies"]
                print("🎉 登录成功！")
                print(f"获取到cookies长度: {len(self.cookies)} 字符")
                return True
            elif login_status == "waiting":
                print("等待用户扫码...")
                time.sleep(check_interval)
            else:
                print(f"未知登录状态: {login_status}")
                return False
                
        print("⏰ 登录超时，请重新尝试")
        return False
    
    def _save_qrcode_image(self, qrcode_data: str):
        """保存二维码图片到本地"""
        try:
            # 处理base64数据
            if qrcode_data.startswith('data:image'):
                # 去除data:image前缀
                qrcode_data = qrcode_data.split(',')[1]
            
            # 解码base64数据
            image_data = base64.b64decode(qrcode_data)
            
            # 保存到文件
            with open('qrcode.png', 'wb') as f:
                f.write(image_data)
                
            print("✅ 二维码图片已保存为 qrcode.png")
            
        except Exception as e:
            print(f"保存二维码图片失败: {e}")
    
    def login_with_cookies(self, cookies: str, session_id: Optional[str] = None,
                          use_cdp: bool = False, proxy: Optional[str] = None) -> bool:
        """
        使用已有cookies登录
        
        Args:
            cookies: cookie字符串
            session_id: 可选的会话ID
            use_cdp: 是否使用CDP模式
            proxy: 代理地址
            
        Returns:
            bool: 登录是否成功
        """
        print("=== 开始Cookie登录流程 ===")
        
        login_data = {
            "login_type": "cookies",
            "cookies": cookies,
            "use_cdp": use_cdp,
            "proxy": proxy
        }
        if session_id:
            login_data["session_id"] = session_id
            
        try:
            result = self._make_request("POST", "/creator/login", login_data)
            
            if result["status"] == "success":
                self.session_id = result["session_id"]
                self.cookies = result["cookies"]
                print(f"✅ Cookie登录成功，会话ID: {self.session_id}")
                return True
            else:
                print(f"Cookie登录失败: {result}")
                return False
                
        except Exception as e:
            print(f"Cookie登录失败: {e}")
            return False
    
    def get_creator_data(self) -> Optional[dict]:
        """
        获取创作者平台数据
        
        Returns:
            dict: 创作者数据，失败返回None
        """
        if not self.cookies or not self.session_id:
            print("❌ 请先登录")
            return None
            
        print("=== 获取创作者数据 ===")
        
        try:
            result = self._make_request("POST", "/creator/data", {
                "session_id": self.session_id,
                "cookies": self.cookies
            })
            
            if result["status"] == "success":
                print("✅ 创作者数据获取成功")
                return result["data"]
            else:
                print(f"获取创作者数据失败: {result}")
                return None
                
        except Exception as e:
            print(f"获取创作者数据失败: {e}")
            return None
    
    def list_sessions(self) -> list:
        """列出所有活跃会话"""
        try:
            result = self._make_request("GET", "/creator/sessions")
            if result["status"] == "success":
                return result["sessions"]
            return []
        except Exception as e:
            print(f"获取会话列表失败: {e}")
            return []
    
    def cleanup_session(self) -> bool:
        """清理当前会话"""
        if not self.session_id:
            return True
            
        try:
            result = self._make_request("DELETE", f"/creator/session/{self.session_id}")
            if result["status"] == "success":
                print(f"✅ 会话 {self.session_id} 已清理")
                self.session_id = None
                self.cookies = None
                return True
            return False
        except Exception as e:
            print(f"清理会话失败: {e}")
            return False
    
    def cleanup_all_sessions(self) -> bool:
        """清理所有会话"""
        try:
            result = self._make_request("POST", "/creator/sessions/cleanup")
            if result["status"] == "success":
                print(f"✅ {result.get('message', '清理完成')}")
                return True
            return False
        except Exception as e:
            print(f"清理所有会话失败: {e}")
            return False


def test_concurrent_safety():
    """测试并发安全性 - 演示多个会话可以同时工作"""
    import threading
    import time
    
    print("🔒 测试并发安全性...")
    
    def create_session(session_id):
        client = DouyinCreatorAPIClient("http://localhost:8000")
        try:
            response = requests.post('http://localhost:8000/api/douyin/creator/qrcode/get', json={
                "session_id": session_id
            })
            if response.status_code == 200:
                print(f"✅ 会话 {session_id}: 二维码获取成功")
                return True
            else:
                print(f"❌ 会话 {session_id}: 二维码获取失败")
                return False
        except Exception as e:
            print(f"❌ 会话 {session_id}: 出现异常 - {e}")
            return False
    
    # 创建多个并发会话
    threads = []
    for i in range(3):
        thread = threading.Thread(target=create_session, args=[f"test_session_{i}"])
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print("🔒 并发安全性测试完成")


def main():
    """主函数 - 演示完整的使用流程"""
    print("🚀 抖音创作者平台API客户端示例")
    print("=" * 50)
    
    # 初始化客户端
    client = DouyinCreatorAPIClient("http://localhost:8000")
    
    try:
        # 方式一：二维码登录（推荐）
        print("\n选择登录方式：")
        print("1. 二维码登录（推荐）")
        print("2. Cookie登录")
        print("3. 查看活跃会话")
        print("4. 清理所有会话")
        print("5. 测试并发安全性")
        
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == "1":
            # 二维码登录
            success = client.login_with_qrcode()
            if success:
                # 获取创作者数据
                data = client.get_creator_data()
                if data:
                    print("\n📊 创作者数据预览:")
                    print(json.dumps(data, indent=2, ensure_ascii=False)[:1000] + "...")
                    
                    # 保存完整数据到文件
                    with open('creator_data.json', 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2, ensure_ascii=False)
                    print("📁 完整数据已保存到 creator_data.json")
                    
        elif choice == "2":
            # Cookie登录
            cookies = input("请输入cookies: ").strip()
            if cookies:
                success = client.login_with_cookies(cookies)
                if success:
                    data = client.get_creator_data()
                    if data:
                        print("\n📊 创作者数据预览:")
                        print(json.dumps(data, indent=2, ensure_ascii=False)[:1000] + "...")
                        
        elif choice == "3":
            # 查看会话
            sessions = client.list_sessions()
            print(f"\n📋 当前活跃会话数量: {len(sessions)}")
            for session in sessions:
                print(f"  - {session}")
                
        elif choice == "4":
            # 清理所有会话
            client.cleanup_all_sessions()
            
        elif choice == "5":
            # 测试并发安全性
            test_concurrent_safety()
            
        else:
            print("❌ 无效的选择")
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    finally:
        # 清理资源
        if client.session_id:
            print("\n🧹 清理会话资源...")
            client.cleanup_session()


if __name__ == "__main__":
    main() 