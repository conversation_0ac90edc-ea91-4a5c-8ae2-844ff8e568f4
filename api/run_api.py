#!/usr/bin/env python3
# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：  
# 1. 不得用于任何商业用途。  
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。  
# 3. 不得进行大规模爬取或对平台造成运营干扰。  
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。   
# 5. 不得用于任何非法或不当的用途。
#   
# 详细许可条款请参阅项目根目录下的LICENSE文件。  
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。  

"""
抖音API服务启动脚本

提供抖音创作者平台相关的HTTP API服务，包括：
- 二维码登录
- 获取创作者数据
- 会话管理

使用方法:
    python api/run_api.py
    或者
    uv run api/run_api.py

API文档访问: http://localhost:8001/docs
"""

import os
import sys
import uvicorn
import argparse

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from api.douyin_api import app


def main():
    parser = argparse.ArgumentParser(description='启动抖音API服务')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='服务绑定的主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8001, help='服务端口 (默认: 8001)')
    parser.add_argument('--reload', action='store_true', help='启用自动重载 (开发模式)')
    parser.add_argument('--log-level', type=str, default='info', 
                       choices=['critical', 'error', 'warning', 'info', 'debug', 'trace'],
                       help='日志级别 (默认: info)')
    
    args = parser.parse_args()
    
    print(f"""
🚀 启动抖音API服务...

服务地址: http://{args.host}:{args.port}
API文档: http://{args.host}:{args.port}/docs
Swagger UI: http://{args.host}:{args.port}/redoc

支持的功能:
- 🔐 抖音创作者平台二维码登录
- 📊 获取创作者数据
- 💼 会话管理

按 Ctrl+C 停止服务
""")
    
    try:
        uvicorn.run(
            "api.douyin_api:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level,
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 API服务已停止")
    except Exception as e:
        print(f"❌ 启动API服务失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()