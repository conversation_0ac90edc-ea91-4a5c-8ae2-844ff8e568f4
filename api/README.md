# MediaCrawler API 服务

本目录包含 MediaCrawler 的 HTTP API 服务实现，可以通过 HTTP 接口调用爬虫功能。

## 功能列表

目前支持以下功能：

- 抖音创作者平台登录（二维码登录 + Cookie 登录）
- 获取抖音创作者平台数据
- 会话管理（创建、使用、删除会话）

## 启动服务

```bash
python -m api.run_api
```

服务默认在 `http://localhost:8000` 启动。

## API 文档

启动服务后，可以通过访问 `http://localhost:8000/docs` 查看 API 文档。

## 改进说明

**重要改进**：为了适配外部 API 调用场景和确保并发安全，我们对登录流程进行了优化：

1. **新增分离式二维码登录**：适合外部客户端使用

   - `/creator/qrcode/get` - 获取二维码图片
   - `/creator/qrcode/status` - 轮询检查登录状态

2. **🔒 并发安全保障**：每个会话使用独立的浏览器实例

   - 每次请求都创建全新的浏览器实例
   - 完全隔离不同用户的登录环境
   - 避免 cookie 互相干扰

3. **智能资源管理**：自动清理浏览器实例和会话

   - 登录成功后自动关闭浏览器
   - 提供过期会话清理功能
   - 支持手动会话管理

4. **性能优化**：并行选择器 + 智能回退策略
   - 快速并行模式：同时尝试多个选择器
   - 自动回退到串行模式
   - 详细的调试信息输出

## API 使用示例

### 推荐的二维码登录流程（适合外部调用）

#### 1. 获取登录二维码

```bash
curl -X 'POST' \
  'http://localhost:8000/api/douyin/creator/qrcode/get' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "session_id": "my_unique_session_123",
  "use_cdp": false,
  "proxy": null
}'
```

响应示例：

```json
{
  "status": "success",
  "message": "二维码获取成功",
  "session_id": "my_unique_session_123",
  "qrcode_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "expires_in": 300
}
```

#### 2. 显示二维码给用户扫码

在你的客户端界面中显示返回的 `qrcode_image`（base64 格式图片）

#### 3. 轮询检查登录状态

```bash
curl -X 'POST' \
  'http://localhost:8000/api/douyin/creator/qrcode/status' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "session_id": "my_unique_session_123"
}'
```

等待中的响应：

```json
{
  "status": "success",
  "login_status": "waiting",
  "message": "等待用户扫码登录"
}
```

登录成功的响应：

```json
{
  "status": "success",
  "login_status": "logged_in",
  "message": "登录成功",
  "cookies": "your_cookies_string_here"
}
```

### Cookie 登录（如果已有 cookies）

```bash
curl -X 'POST' \
  'http://localhost:8000/api/douyin/creator/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "login_type": "cookies",
  "session_id": "my_unique_session_123",
  "cookies": "your_cookies_here",
  "use_cdp": false,
  "proxy": null
}'
```

### 获取抖音创作者平台数据

```bash
curl -X 'POST' \
  'http://localhost:8000/api/douyin/creator/data' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "session_id": "my_unique_session_123",
  "cookies": "your_cookies_here",
  "use_cdp": false,
  "proxy": null
}'
```

### 会话管理

#### 列出所有会话

```bash
curl -X 'GET' \
  'http://localhost:8000/api/douyin/creator/sessions' \
  -H 'accept: application/json'
```

响应示例：

```json
{
  "status": "success",
  "sessions": [
    {
      "session_id": "my_unique_session_123",
      "status": "logged_in",
      "has_cookies": true,
      "has_browser_context": false
    }
  ],
  "count": 1
}
```

#### 删除指定会话

```bash
curl -X 'DELETE' \
  'http://localhost:8000/api/douyin/creator/session/my_unique_session_123' \
  -H 'accept: application/json'
```

#### 清理所有会话

```bash
curl -X 'POST' \
  'http://localhost:8000/api/douyin/creator/sessions/cleanup' \
  -H 'accept: application/json'
```

#### 清理过期会话（推荐）

```bash
curl -X 'POST' \
  'http://localhost:8000/api/douyin/creator/sessions/cleanup?expired_only=true' \
  -H 'accept: application/json'
```

## 完整的客户端使用流程

### 方式一：二维码登录（推荐）

```python
import requests
import time
import base64
from io import BytesIO
from PIL import Image

# 1. 获取二维码
response = requests.post('http://localhost:8000/api/douyin/creator/qrcode/get', json={
    "session_id": "my_session_123"
})
data = response.json()
session_id = data['session_id']
qrcode_base64 = data['qrcode_image']

# 2. 显示二维码（这里简单保存为文件）
if qrcode_base64.startswith('data:image'):
    qrcode_base64 = qrcode_base64.split(',')[1]
qrcode_data = base64.b64decode(qrcode_base64)
with open('qrcode.png', 'wb') as f:
    f.write(qrcode_data)
print("请扫描 qrcode.png 文件中的二维码")

# 3. 轮询检查登录状态
cookies = None
for i in range(60):  # 最多等待5分钟
    response = requests.post('http://localhost:8000/api/douyin/creator/qrcode/status', json={
        "session_id": session_id
    })
    status_data = response.json()

    if status_data.get('login_status') == 'logged_in':
        cookies = status_data['cookies']
        print("登录成功！")
        break
    elif status_data.get('login_status') == 'waiting':
        print("等待扫码...")
        time.sleep(5)
    else:
        print("登录失败")
        break

# 4. 获取创作者数据
if cookies:
    response = requests.post('http://localhost:8000/api/douyin/creator/data', json={
        "session_id": session_id,
        "cookies": cookies
    })
    creator_data = response.json()
    print("创作者数据：", creator_data)
```

### 方式二：Cookie 登录

```python
import requests

# 如果已有cookies，直接使用
cookies = "your_existing_cookies_here"

response = requests.post('http://localhost:8000/api/douyin/creator/login', json={
    "login_type": "cookies",
    "session_id": "my_session_123",
    "cookies": cookies
})

if response.json()['status'] == 'success':
    # 获取数据
    response = requests.post('http://localhost:8000/api/douyin/creator/data', json={
        "session_id": "my_session_123",
        "cookies": cookies
    })
    creator_data = response.json()
    print("创作者数据：", creator_data)
```

## 返回数据格式

所有 API 返回的数据格式均为 JSON，包含以下字段：

- `status`: 请求状态，成功为 `success`，失败为 `error`
- `cookies` 或 `data`: 根据接口不同，返回相应的数据
- `session_id`: 会话 ID，用于后续请求

如果请求失败，会返回 HTTP 错误状态码和错误详情：

```json
{
  "detail": "错误详情"
}
```

## 会话管理说明

为了支持跨服务器场景下的使用，API 接口采用会话 ID 机制管理不同的爬虫实例：

1. **会话隔离**：每个 `session_id` 都有独立的浏览器实例和 cookie 状态
2. **资源管理**：系统会自动管理浏览器实例的创建和销毁
3. **状态跟踪**：可以查看每个会话的登录状态和资源使用情况
4. **清理机制**：提供会话清理接口，避免资源泄露

### 会话状态说明

- `qrcode_generated`: 二维码已生成，等待扫码
- `logging_in`: 正在登录过程中
- `logged_in`: 已成功登录
- `unknown`: 状态未知

## 注意事项

1. **🔒 并发安全**：每个会话使用独立浏览器实例，完全隔离，支持多用户并发
2. **💾 浏览器资源**：API 服务会启动 headless 浏览器，请确保服务器有足够资源
3. **⏰ 会话管理**：
   - 二维码有效期为 5 分钟
   - 建议定期清理过期会话（超过 10 分钟）
   - 登录成功后浏览器实例会自动关闭
4. **🔐 Cookie 安全**：请妥善保管获取到的 cookies，不要泄露给他人
5. **🧹 资源清理**：
   - 使用完毕后建议主动删除会话
   - 可以使用 `expired_only=true` 只清理过期会话
   - 系统会自动处理异常关闭的浏览器实例

这种设计使得不同的客户端可以独立管理各自的会话，避免了会话状态混淆的问题，特别适合外部 API 调用场景。
